from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger

def understand_standard_agent(cluster_statement:str,common_core_standard:str,grade:str,subject:str,topic:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
       
    messages = langfuse.get_prompt('understand_standard', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        cluster_statement=cluster_statement,
        common_core_standard=common_core_standard,
        grade=grade,
        subject=subject,
        topic=topic
        )

    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens)
    return result