from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.generate_rubrics import generate_rubric_questions_agent
from service.context_pipeline import logger

def rubric_questions_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node for generating rubric questions for assignments.

    Takes the current state and processes the assignment to add rubric questions.
    """
    logger.info(f"Starting rubric_questions_node:{state}")
    if state.request.passage:
        passage=state.request.passage
    else:
        passage=state.ai_passage
    try:
        updated_assignment = generate_rubric_questions_agent(
            assignment=state.assignment,
            standard=state.request.standard,
            assignment_type=state.request.assignment_type,
            passage=passage,
            grammar=state.request.needs_grammar_focus,
            grade=state.student.grade,
            difficulty=state.request.difficulty,
            nb= state.request.nb_of_questions
        )
        logger.info("Completed rubric_questions_node successfully")
    except Exception as e:
        logger.error(f"Error in rubric_questions_node: {e}")
        updated_assignment = state.assignment  # Return original assignment on error
    logger.info(f"State after rubric questions node:{state}")
    return {"assignment": updated_assignment}