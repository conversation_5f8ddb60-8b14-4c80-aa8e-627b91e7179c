# from service.openai_utils.gpt_call import make_openai_langfuse
# from service.context_pipeline import langfuse
# from service.context_pipeline import logger
# import json
# def develop_tasks_passage_agent(refined_passage:str,sub_standard:dict,grade:str) -> dict:
#     """
#     Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

#     Args:
#         original_assignment (str): Original student assignment content.
#         adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

#     Returns:
#         dict: Revised assignment and implemented adjustments.
#     """
            
#     messages = langfuse.get_prompt('develop_tasks_passage', type='chat', label="latest")

#     complete_chat_prompt = messages.compile(
#         passage=refined_passage,
#         sub_standard=sub_standard,
#         grade=grade
#     )
#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "generate_skill_demonstrations",
#                 "description": (
#                     "Produces an ordered list of skill objects, each containing a skill "
#                     "description and a corresponding demonstration example."
#                 ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "skills": {
#                             "type": "array",
#                             "description": (
#                                 "A list of skill objects. Each object must include a "
#                                 "'skill' field and a 'demonstration_example' field."
#                             ),
#                             "items": {
#                                 "type": "object",
#                                 "properties": {
#                                     "skill_number": {
#                                         "type": "string",
#                                         "description": "The number of the skill"
#                                     },
#                                     "task": {
#                                         "type": "string",
#                                         "description": "A task that measures the skill."
#                                     }
#                                 },
#                                 "required": ["skill_number", "task"]
#                             }
#                         }
#                     },
#                     "required": ["skills"]
#                 }
#             }
#         }
#     ]
#     temperature = messages.config.get("openai", {}).get("temperature", 0.5)
#     model = messages.config.get("openai", {}).get("model", "gpt-4.1")
#     max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
#     result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
#     return result['skills']

from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse, logger
import json
from typing import List, Dict

def develop_tasks_passage_agent( refined_passage: str, sub_standard: list, grade: str, nb_of_questions: int , assignment_type: str ) -> List[Dict]:
    """
    Generates `nb_of_questions` passage-based tasks.

    Behaviour
    ----------
    1. If `nb_of_questions` <= len(sub_standard)  ➔ one model call with the first
       `nb_of_questions` sub-standards.
    2. If `nb_of_questions`  > len(sub_standard) ➔ keep looping through the
       sub-standards list, making additional model calls, until the requested
       number of tasks is reached.

    Args
    ----
    refined_passage : str
        The reading passage (already refined).
    sub_standard : list[dict] | str
        List (or JSON string) of sub-standard objects.
    grade : str | None
        Grade level.
    nb_of_questions : int
        Total number of passage tasks required.

    Returns
    -------
    list[dict]
        A list of task objects with at least:
        { "skill_number": <str>, "task": <str> }
    """
    if assignment_type == 'history_fact':
        additional_instruction = (
            "Generate only fact-based questions. "
            "Each question must have a definite, unambiguous answer found explicitly in the provided passage. "
            "Do not generate questions that require opinion, inference, interpretation, speculation, or external knowledge. "
            "Avoid open-ended, evaluative, or hypothetical questions. "
            "Use only the facts presented in the passage, and format the questions so that each answer can be found as a clear statement within the text."
        )
    else:
        additional_instruction = ""    
    # ------------------------------------------------------------------ #
    # Convert JSON-string to list if necessary                            #
    # ------------------------------------------------------------------ #
    if isinstance(sub_standard, str):
        try:
            sub_standard = json.loads(sub_standard)
        except Exception as e:
            logger.error(f"Failed to convert sub_standard JSON: {e}")
            sub_standard = []

    total_substandards = len(sub_standard)
    tasks_generated = []

    # ------------------------------------------------------------------ #
    # Prompt, tool, and model configuration                               #
    # ------------------------------------------------------------------ #
    messages     = langfuse.get_prompt("develop_tasks_passage", type="chat", label="latest")
    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_tasks",
                "description": (
                    "Produces an ordered list of skill objects, each containing "
                    "a skill_number and a task"
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skills": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill_number' field and a 'task' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {"type": "string",
                                                     "description": "The number of the skill"},
                                    "task": {"type": "string",
                                             "description": "A task that measures the skill.",}
                                },
                                "required": ["skill_number", "tasks"]
                            }
                        }
                    },
                    "required": ["skills"]
                }
            }
        }
    ]
    temperature  = messages.config.get("openai", {}).get("temperature", 0.5)
    model        = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens   = messages.config.get("openai", {}).get("max_tokens",  4096)

    logger.info(f'inside develop_tasks_passage_agent:{nb_of_questions} and {tasks_generated}')
    if type(nb_of_questions)==tuple:
        nb_of_questions=nb_of_questions[0]
    while len(tasks_generated) < nb_of_questions and total_substandards:
        remaining_needed = nb_of_questions - len(tasks_generated)
        # if remaining_needed % 2 == 0:
        #     remaining_needed=remaining_needed//2
        # else:
        #     remaining_needed=(remaining_needed//2)+1
        # Decide how many sub-standards to send in this call
        current_slice = (
            sub_standard[:remaining_needed]
            if remaining_needed < total_substandards
            else sub_standard
        )

        # Build prompt
        complete_chat_prompt = messages.compile(
            passage      = refined_passage,
            sub_standard = current_slice,
            grade_info        = grade,
            additional_instruction = additional_instruction
        )

        # Model call
        result = make_openai_langfuse(
            complete_chat_prompt,
            model        = model,
            temperature  = temperature,
            max_tokens   = max_tokens,
            tools        = tools
        )
        # output = []
        # for item in result['skills']:
        #     skill_number = item['skill_number']
        #     for task in item['tasks']:
        #         output.append({'skill_number': skill_number, 'task': task})

        # Post-process returned tasks
        for task in result['skills']:
            sn = task.get("skill_number")
            # info = skill_map.get(sn, {})
            # task.setdefault("skill", info.get("skill"))
            # task.setdefault("demonstration_example", info.get("demonstration_example"))
            tasks_generated.append(task)
        # Append returned tasks

    # Return exactly the amount requested
    return tasks_generated[:nb_of_questions]