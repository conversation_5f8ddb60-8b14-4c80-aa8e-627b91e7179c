from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
def develop_tasks__passage_agent(refined_passage:str,sub_standard:dict,grade:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
            
    messages = langfuse.get_prompt('develop_tasks_passage', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        passage=refined_passage,
        sub_standard=sub_standard,
        grade=grade
    )
    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_skill_demonstrations",
                "description": (
                    "Produces an ordered list of skill objects, each containing a skill "
                    "description and a corresponding demonstration example."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skills": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill' field and a 'demonstration_example' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {
                                        "type": "string",
                                        "description": "The number of the skill"
                                    },
                                    "task": {
                                        "type": "string",
                                        "description": "A task that measures the skill."
                                    }
                                },
                                "required": ["skill_number", "task"]
                            }
                        }
                    },
                    "required": ["skills"]
                }
            }
        }
    ]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
    return result['skills']
