from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object
from service.common_functions import inject_passage_field

def translate_assignment_agent(json_response: dict, nb: int, assignment_type: str, tools: dict, ai_passage: str = "", language: str = "english") -> dict:
    """
    Core agent functionality for translating assignments to different languages.

    Translates from English into the specified target language while maintaining original meaning,
    educational intent, and context. Adapts content to be clear and culturally appropriate.

    Args:
        json_response: The JSON object containing the assignment data
        nb: The number of items or problems in the assignment
        assignment_type: The type of assignment to be translated
        tools: Tools required for processing the OpenAI request
        language: The target language for translation (default: "english")

    Returns:
        dict: Translated assignment retaining original structure and educational purpose
    """
    logger.info(f"Starting translate_assignment_agent for {assignment_type} to {language}")
    try:
        logger.debug("Retrieving translation_agent prompt from langfuse")
        additional_instructions=""
        if 'essay' in assignment_type.lower():
            additional_instructions+=""" *Bullet Formatting*: Format the essay prompt in clear bullet points. Each point must be separated by '\\n\\n' for readability. Ensure both instructions and the main essay prompt are formatted as bullets. Use this structure: - Take a clear stance on the issue.\n\n - Provide specific evidence to support your argument.\n\n - Organize your ideas effectively and logically.\n\n - Express your thoughts clearly and persuasively. """
        if ai_passage:
            if 'homework' in json_response:
                json_response['homework']['passage']=ai_passage
            else:
                json_response['passage']=ai_passage
        messages = langfuse.get_prompt('translation_agent', type='chat', label="latest")
        complete_chat_prompt = messages.compile(
            nb=nb,
            language=language,
            json_response=json_response,
            additional_instructions=additional_instructions,
        )
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        if ai_passage and assignment_type in ['reading_comp_gen','vocab_fill']:
            temp_tools=inject_passage_field(tools.copy())
        else:
            temp_tools=tools
        logger.debug(f"Making OpenAI request for translation:{temp_tools}")
        if nb ==1:
            assignment = make_openai_langfuse(complete_chat_prompt, temp_tools, model=model, temperature=temperature, max_tokens=max_tokens)
            if isinstance(assignment["homework"]["challenges"], dict):
                assignment["homework"]["challenges"] = [assignment["homework"]["challenges"]]
        else:
            assignment = make_openai_langfuse(complete_chat_prompt, temp_tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb)
        if 'passage' in assignment:
            passage=assignment.pop('passage')
        else:
            passage=""
        logger.info(f"Successfully translated assignment to {language}")
        return assignment, passage
    
    except Exception as e:
        logger.error(f"Error in translate_assignment_agent: {e}")
        return json_response, ""