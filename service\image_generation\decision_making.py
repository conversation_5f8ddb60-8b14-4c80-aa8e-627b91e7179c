from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger,langfuse
from service.context_pipeline import GRAPHICS_DICT

def decision_maker(assignment_question, standard_used, grade_level):
    logger.info("image_generation_condition")
    system_prompt = """
You are an educational math content expert specializing in evaluating suitable visual enhancements for math assignments questions aimed at increasing clarity, engagement, and understanding of mathematics concepts for students.

Input:
You will receive the following clearly labeled inputs:

"assignment_question": containing fields ("scenario", "task", "title", "answerKey", "index")
"standard_used": detailed educational standard (including domain, objective, common core code, cluster statement)
"grade_level": Student's targeted educational level

Steps for Decision-making:
Carefully read and analyze the provided scenario, task, standard used, and student grade. Choose exactly ONE of these three visual enhancement options:

"diagram": Only pick when a SIMPLE mathematical visualization like number lines, a basic geometric shape, simple bar/pie charts, coordinate-plane plots, or visual relationships can be clearly drawn programmatically through coding (e.g., matplotlib).
"table": Only pick when there's structured numeric or categorical information such as frequencies, comparisons, measurements, or summarized data that clearly benefit student understanding.
"image": Pick as the default option or when a photograph or illustration would genuinely enhance student engagement, context comprehension, or clearly depicts a scenario/context visually (images cannot be programmatically drawn via matplotlib—thus must be manually selected).

Important Explicit Constraints/Reminders:

DO NOT choose "diagram" for detailed illustrations requiring manual images or graphics. A diagram must be explicitly drawable using standard coding libraries like matplotlib.
DO NOT EVER include or reveal the final ANSWER or solution explicitly in your description of visual option. (e.g., total sums or final results should NOT appear on diagrams/images/tables).
ALWAYS provide a brief & clear description explicitly stating what the visual (diagram/image/table) would clearly look like or represent, WITHOUT revealing the answer.
ALWAYS reference explicitly the scenario context, standard alignment, and student grade in reasoning explicitly.

Expected Output (strictly follow JSON format below):
Your decision must ONLY be in the following format, no additional text allowed:

{
"choice": "<diagram/image/table>",
"reasoning": "<concise reasoning (2-3 sentences max) explicitly referencing scenario, math standard alignment, and student grade>",
"description": "<clear brief description explicitly stating visual contents WITHOUT any answers included>"
}

This explicit instruction now differentiates clearly the visual types and expectations, including emphasis on explicitly avoiding the inclusion of answers.
"""

    user_prompt = f"""
Assignment question:
{assignment_question}

Standard used:
{standard_used}

Grade level:
{grade_level}

Main Task:
Carefully analyze the provided math assignment question, standard used, and targeted student's grade level. Determine whether including one—and only one—of these visual enhancements ("image", "table", "diagram") would significantly enhance student understanding, clarify the content, or increase engagement. If no clear choice emerges, select "image" as the default option.

Important reminders:

Clearly reference scenario, standard, and student's grade explicitly when formulating your reasoning.
Choose ONLY ONE option that best fits the assignment scenario and standard: "image", "table", "diagram".
Provide your answer strictly in the following JSON format only:
{{
"choice": "<image | table | diagram>",
"reasoning": "clear justification, 2–3 sentences explicitly referencing scenario, standard, and grade level",
"description": "clear brief description explicitly stating visual contents WITHOUT any answers included"
}}

Always adhere strictly to the provided JSON structure.
"""
    tools=[
        {
            "type": "function",
            "function": {
                "name": "image_generation_condition",
                "description": "Decide if an image, table, diagram, or no visual enhancement is needed for the assignment question based on the standard and grade level.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "choice": {
                            "type": "string",
                            "enum": ["image", "table", "diagram"],
                            "description": "The chosen visual enhancement option."
                        },
                        "reasoning": {
                            "type": "string",
                            "description": "Clear, concise explanation justifying the chosen option, explicitly referencing the assignment scenario, standard, and grade level."
                        },
                        "description": {
                            "type": "string",
                            "description": "Clear brief description explicitly stating visual contents WITHOUT any answers included."
                        }
                    },
                    "required": ["choice", "reasoning", "description"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    response = make_openai_langfuse(messages=messages,tools=tools,model="gpt-4.1-nano", temperature=0.7)
    logger.info(f"response: {response}")
    return response





def visual_necessity_decision_maker(assignment_question: str,
                                    standard_used: str,
                                    grade_level: str):
    """
    Decide whether a math assignment could (with or without minor wording
    tweaks) benefit from a programmatically generated graphic.  Return an
    object ready for OpenAI function-calling.
    """

    logger.info("visual_necessity_decision")
    GRAPHIC_CATEGORIES = ", ".join(GRAPHICS_DICT.keys())
    # system_prompt = f"""
    #     ROLE:
    #     You are a K-12 Mathematics Pedagogy Specialist, specializing in enhancing student engagement through clear and effective visual aids.

    #     AVAILABLE GRAPHIC CATEGORIES:
    #     {GRAPHIC_CATEGORIES}

    #     TASK:
    #     Evaluate the provided math assignment question based on its standard and grade level to decide whether providing a visual graphic would enhance or hinder intended student learning objectives.

    #     ACTIONS REQUIRED:
    #     1. Determine if adding a visual graphic from the provided categories will help students better engage with or understand the assignment.
    #     2. Check explicitly if the question instructs students explicitly to independently draw, visualize, construct, sketch, or graph something themselves.
    #     3. Clearly decide if MINOR adjustments (small wording changes, phrasing refinements, slight numeric adjustments) to the existing question would be required to effectively accommodate the chosen visual WITHOUT affecting the original learning objectives or standard.
    #     4. Select exactly ONE graphic category most relevant to visually improve the student's experience. If no graphic is beneficial, explicitly choose 'none'.

    #     ADDITIONAL EXPLICIT RULES (MUST ALWAYS FOLLOW):
    #     - IF the assignment explicitly instructs students to independently visualize, draw, sketch, plot, graph, construct, or create something graphically themselves:
    #     - explicitly set "use_graphic" to false.
    #     - explicitly set "adjust_question" to false.
    #     - explicitly set "recommended_category" explicitly to "none".
    #     - ELSE IF "use_graphic" is false:
    #     - set "adjust_question" explicitly to false.
    #     - set "recommended_category" explicitly to "none".
    #     - IF "use_graphic" is true and no minor tweak needed:
    #     - set "adjust_question" explicitly to false.
    #     - Select only ONE most suitable category.
    #     - NEVER include solutions or deviate from the assignment's original learning goals or standard.

    #     RESPONSE FORMAT (Strictly JSON - NO additional commentary or keys):
    #     {{
    #     "use_graphic": <true|false>,
    #     "adjust_question": <true|false>,
    #     "recommended_category": "<selected category name from provided categories or 'none'>",
    #     "reasoning": "<Concise and clear rationale referencing the question scenario, specified standard, and student's grade level in maximum 2-3 sentences>"
    #     }}
    #     """.strip()
    ##############################################################################################################################
    #ABOVE PROMPT IS WITH ADJUST QUESTION WHICH WE HAVE DISABleD foR NOW
    ##############################################################################################################################
    # system_prompt = f"""
    #     ROLE:
    #     You are a K-12 Mathematics Pedagogy Specialist, specializing in enhancing student engagement through clear and effective visual aids.

    #     AVAILABLE GRAPHIC CATEGORIES:
    #     {GRAPHIC_CATEGORIES}

    #     TASK:
    #     Evaluate the provided math assignment question based on its standard and grade level to decide whether providing a visual graphic would enhance or hinder intended student learning objectives.

    #     ACTIONS REQUIRED:
    #     1. Determine if adding a visual graphic from the provided categories will clearly help students better engage with or understand the assignment.
    #     2. Check explicitly if the question instructs students explicitly to independently draw, visualize, construct, sketch, or graph something themselves.
    #     3. Select exactly ONE graphic category most relevant to visually improve the student's experience. If no graphic is beneficial, explicitly choose 'none'.

    #     EXPLICIT RULES (MUST ALWAYS FOLLOW):
    #     - IF the assignment explicitly instructs students to independently visualize, draw, sketch, plot, graph, construct, or create something graphically themselves:
    #     - explicitly set "use_graphic" to false.
    #     - explicitly set "recommended_category" explicitly to "none".
    #     - IF "use_graphic" is false:
    #     - explicitly set "recommended_category" to "none".
    #     - IF "use_graphic" is true:
    #     - Select explicitly and clearly exactly ONE most suitable category.
    #     - NEVER include solutions or deviate from the assignment's original learning goals or standard.
    #     - NEVER recommend adjusting or changing the original assignment question.

    #     RESPONSE FORMAT (Strictly JSON - NO additional commentary or keys):
    #     {{
    #     "use_graphic": <true|false>,
    #     "recommended_category": "<selected category name from provided categories or 'none'>",
    #     "reasoning": "<Concise and clear rationale referencing the question scenario, specified standard, and student's grade level in maximum 2-3 sentences>"
    #     }}
    #     """.strip()
    # system_prompt = f"""
    # You are a K-12 Mathematics Pedagogy Specialist, specializing in enhancing student engagement through clear and effective visual aids.

    # AVAILABLE GRAPHIC CATEGORIES:
    # {GRAPHIC_CATEGORIES}

    # TASK:
    # Evaluate the provided math assignment question based on its standard and grade level to decide explicitly whether providing a visual graphic will clearly enhance (or hinder) intended student learning objectives.

    # ACTIONS REQUIRED:
    # 1. Determine clearly if adding a visual from provided categories explicitly helps student engagement or comprehension.
    # 2. Explicitly check if the question instructs students explicitly to independently draw, visualize, construct, sketch, or graph something themselves:
    # IF YES explicitly: Explicitly set visuals as NOT needed (choose NONE clearly).
    # 3. Explicitly check the ADDITIONAL KEYWORD-BASED CRITERIA explicitly provided below, setting visuals explicitly as NOT needed if matched.
    # 4. Select explicitly ONE most relevant visual category or explicitly choose 'none'.

    # EXPLICIT RULES (MUST ALWAYS FOLLOW):
    # 1. If the assignment explicitly instructs students explicitly to independently visualize, draw, sketch, plot, graph, construct, or create visuals explicitly themselves: DO NOT USE GRAPHIC (choose NONE explicitly).
    # 2. Explicitly DO NOT recommend visual graphics if the question explicitly contains instructions or one or more keywords explicitly indicating tasks involving:
    # - Clear Verbal or Conceptual explanations clearly:
    #     (keywords explicitly include: "explain", "justify", "discuss", "argue", "interpret", "describe", "define")
    # - Explicit Mental Calculation and Mental Visualization:
    #     (keywords explicitly include: "mentally calculate", "mentally visualize", "calculate without visuals", "imagine mentally")
    # - Explicit Comparative Reasoning explicitly without visual aids explicitly:
    #     (keywords explicitly include: "compare conceptually", "evaluate logically", "determine clearly without visual", "reason clearly which is greater/less than")
    # 3. IF none of the above criteria explicitly triggered, only then clearly and explicitly recommend ONE suitable graphic category explicitly. OTHERWISE explicitly recommended_category = 'none'.
    # 4. NEVER explicitly include solutions or deviate explicitly from existing academic standards or learning goals.

    # RESPONSE FORMAT (Strictly JSON - NO additional commentary or keys):
    # {{
    # "use_graphic": <true|false>,
    # "recommended_category": "<selected category name from provided categories or 'none'>",
    # "reasoning": "<Concise explicit rationale referencing explicitly scenario, standard explicitly, and explicitly student's grade (max. 2-3 sentences)."
    # }}
    # """.strip()
    # user_prompt = f"""
    # Assignment question:
    # {assignment_question}

    # Academic Standard:
    # {standard_used}

    # Grade level:
    # {grade_level}

    # YOUR TASK:
    # Based explicitly on the instructions and constraints in the system prompt, decide clearly whether to include a graphic and whether minor question adjustments are needed. Respond clearly, explicitly, and ONLY with the JSON object defined in the system prompt.
    # """.strip()

    chat_prompt=langfuse.get_prompt('image_decision_maker', type='chat', label="latest")
    messages = chat_prompt.compile(
                                            assignment_question=assignment_question,
                                            standard_used=standard_used,
                                            grade_level=grade_level
                                        )
    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)

    tools = [{
        "type": "function",
        "function": {
            "name": "visual_necessity_decision",
            "description": ("Return whether to use a graphic, whether a minor "
                            "adjustment is needed, the category, and reasoning."),
            "parameters": {
                "type": "object",
                "properties": {
                    "use_graphic": {
                        "type": "boolean",
                        "description": "True if a graphic should be added."
                    },
                    # "adjust_question": {
                    #     "type": "boolean",
                    #     "description": "True if minor tweaks are required to enable the visual."
                    # },
                    "recommended_category": {
                        "type": "string",
                        "description": "Chosen category name, or 'none' if not using a graphic."
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "2-3 sentences referencing scenario, standard, grade."
                    }
                },
                "required": ["use_graphic", "adjust_question",
                             "recommended_category", "reasoning"]
            }
        }
    }]

    # messages = [
    #     {"role": "system", "content": system_prompt},
    #     {"role": "user",   "content": user_prompt}
    # ]

    response = make_openai_langfuse(
        messages=messages,
        tools=tools,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens
    )

    logger.info(f"visual_necessity_decision_response: {response}")
    return response