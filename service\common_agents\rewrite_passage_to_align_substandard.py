from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
def rewrite_passage_to_align_substandard_agent(passage:str,sub_standard:dict,justification:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
            
    messages = langfuse.get_prompt('rewrite_passage_to_align_substandard ', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        passage=passage,
        sub_standard=sub_standard,
        justification=justification
    )
    tools = tools = [{
        "type": "function",
        "function": {
            "name": "rewrite_passage",
            "description": "Rewrites a passage to align with the provided substandards.",
            "parameters": {
                "type": "object",
                "properties": {
                    "passage": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the rewritten passage"
                        }
                    }
                },
                "required": ["passage"]
            }
        }
    }]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
    return result['passage']
