# service/supporting_graph_functions.py
from service.gen_data_models import GeneratePipelineContext
from langgraph.graph import END
from service.context_pipeline import logger

def start(state: GeneratePipelineContext):
    return state

def route_final_steps(state: GeneratePipelineContext):
    logger.info("--- Routing: Final Steps ---")

    language = state.request.language
    done = state.adjustments_done

    if language != "english" and not done["translated"]:
        return ["translate_assignment"]

    return [END]



def check_if_kindergarten(state: GeneratePipelineContext):
    logger.info(f"Checking if grade is kindergarten: {state.student.grade}")
    if state.student.grade == "kindergarten":
        return "Yes"
    return "No"

def check_if_passage_refinement_needed(state: GeneratePipelineContext):
    logger.info(f"Checking if passage refinement is needed: {state.refine_passage}")
    if state.refine_passage.lower()=='fully addresses':
        return "No"
    return "Yes"
