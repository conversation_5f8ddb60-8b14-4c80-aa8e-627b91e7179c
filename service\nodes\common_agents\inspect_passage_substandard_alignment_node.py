from service.gen_data_models import GeneratePipelineContext
from service.common_agents.inspect_passage_substandard_alignment import inspect_passage_substandard_alignment_agent
from service.context_pipeline import logger

def inspect_passage_substandard_alignment_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting inspect_passage_substandard_alignment_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    passage=state.ai_passage
    
    try:
        response = inspect_passage_substandard_alignment_agent(
            passage=passage,
            sub_standard=sub_standard
        )
        logger.info(f"inspect_passage_substandard_alignment response: {response}")
        alignment=response.pop('alignment','fully addresses').lower()
        if alignment == 'fully addresses':
            return {"refine_passage": alignment}
        else:
            justification=response.pop('justification')
            adjustment_instructions['inspect_passage_justification'] = justification
            return {"refine_passage": alignment,"adjustment_instructions": adjustment_instructions}
    except Exception as e:
        logger.error(f"Error in inspect_passage_substandard_alignment_node: {e}")

