# from service.image_generation.image_generation_prompt import generate_direct_image, inspect_generated_image, edit_image
from service.context_pipeline import aisdk_object
from service.context_pipeline import logger
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
# from service.image_generation.decision_making import decision_maker
# from service.image_generation.diagram_generation import generate_diagram
# from service.image_generation.table_generation import generate_table
from service.image_generation.decision_making import visual_necessity_decision_maker
from service.image_generation.visual_aider import generate_visual_aid_instructions
from service.image_generation.question_adjustment_agent import adjust_question_for_graphic
from service.image_generation.diagram_generation import python_graphic_code_generator, run_python_and_get_png,python_graphic_error_fixer,python_graphic_alignment_checker

# def generate_images_for_assignment_parallel( assignment: dict, assignment_id: str, standard: str, grade: str):
#     """
#     Generate images for every challenge in *parallel* and add the result
#     to each question dict as `image_url`.
#     Returns the (possibly nested) `assignment` object, mirroring the
#     structure of the input.
#     """
#     assignment = assignment.get("homework", assignment)

#     challenges = assignment["challenges"]
#     if type(challenges)==dict:
#         challenges=[challenges]

#     def _task(question):
#         """Small wrapper so we can attach extra args cleanly."""
#         return generate_and_validate_graphics(question, assignment_id, standard, grade)

#     with ThreadPoolExecutor() as pool:
#         future_to_question = {
#             pool.submit(_task, q): q for q in challenges
#         }

#         for future in as_completed(future_to_question):
#             question = future_to_question[future]
#             try:
#                 updated_question=future.result()
#                 logger.info(f"Updated question {updated_question}")
#                 question = updated_question
#             except Exception as exc:                    
#                 print(f"[error] {question=}: {exc}")
#                 question = None

#     return assignment
def generate_images_for_assignment_parallel(
        assignment: dict,
        assignment_id: str,
        standard: str,
        grade: str
):
    """
    Generate images for every challenge in parallel and store the
    returned question dictionaries (or in-place mutations) back into the
    assignment, preserving the original order.
    """
    # Allow top-level or nested "homework" wrapper.
    assignment_root = assignment.get("homework", assignment)

    challenges = assignment_root["challenges"]
    if isinstance(challenges, dict):           # normalize to a list
        challenges = [challenges]

    def _task(question):
        return generate_and_validate_graphics(
            question, assignment_id, standard, grade
        )

    # Parallel execution; results come back in the same order as `challenges`.
    with ThreadPoolExecutor() as pool:
        try:
            updated_challenges = list(pool.map(_task, challenges))
        except Exception as exc:
            logger.exception("Error while generating images: %s", exc)
            raise

    # Write results back and return the full assignment object.
    assignment_root["challenges"] = updated_challenges
    return assignment

# def generate_and_validate_image(question, assignment_id, standard, grade, env='staging'):
#     try:
#         decision = decision_maker(question, standard, grade)
#     except Exception as e: 
#         logger.error("image_generation_condition failed: %s", e)
#         decision = {}
#     # try:
#     #     decision = decision_maker(question, standard, grade)
#     # except Exception as e: 
#     #     logger.error("image_generation_condition failed: %s", e)
#     #     decision = {}
#     try:
#         diagram_code = generate_diagram(question, decision, grade)
#     except Exception as e: 
#         logger.error("generate_diagram failed: %s", e)
#         diagram_code = None
#     # try:
#     #     table_latex = generate_table(question, decision, grade)
#     # except Exception as e:
#     #     logger.error("generate_table failed: %s", e)
#     #     table_latex = None
#     # try:
#     #     generated_image = generate_direct_image(question,decision,grade)
#     #     inspection = inspect_generated_image(generated_image, question)
#     #     if inspection['image_passed']:
#     #         filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
#     #         image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, generated_image)
#     #         logger.info(f'image_bucket_url: {image_bucket_url}')
#     #     elif not inspection['image_passed'] and inspection['editable']:
#     #         edited_image = edit_image(generated_image, inspection, question)
#     #         #edited_inspection = inspect_generated_image(edited_image, question)
#     #         #if edited_inspection['image_passed']:
#     #         filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
#     #         image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, edited_image)
#     #         logger.info(f'image_bucket_url (edited): {image_bucket_url}')
#     # except Exception as e:  # noqa: BLE001, WPS429
#     #     logger.error("generate_direct_image failed: %s", e)
#     #     image_bucket_url = None
#     # question['image_url'] = image_bucket_url
#     # question['decision'] = decision
#     question['diagram_code'] = diagram_code
#     # question['table_latex'] = table_latex
#     return question
    
    

def generate_and_validate_graphics(question, assignment_id, standard, grade, env='staging'):
    try:
        decision = visual_necessity_decision_maker(question, standard, grade)
    except Exception as e: 
        logger.error("visual_necessity_decision_maker failed: %s", e)
        decision = {}
    # try:
    #     if decision.get("adjust_question", False) and decision.get("use_graphic", False):
    #         question = adjust_question_for_graphic(question, standard, grade, decision)
    #     else:
    #         logger.info('No question adjustment needed')
    # except Exception as e: 
    #     logger.error("adjust_question_for_graphic failed: %s", e)
    #     question = question
    if decision.get("use_graphic", False):
        try:
            visual_instructions = generate_visual_aid_instructions(question, standard, grade, decision)
        except Exception as e: 
            logger.error("generate_visual_aid_instructions failed: %s", e)
            visual_instructions = []
        try:
            python_code = python_graphic_code_generator(question, visual_instructions, grade, decision)
        except Exception as e: 
            logger.error("python_graphic_code_generator failed: %s", e)
            python_code = None
        try:
            python_code=python_graphic_alignment_checker(question, visual_instructions, grade, decision, python_code)
        except Exception as e: 
            logger.error("python_graphic_alignment_checker failed: %s", e)
            python_code = None
        try:
            if python_code:
                png_b64 = run_python_and_get_png(python_code)
                filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
                image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, png_b64)
                logger.info(f'image_bucket_url: {image_bucket_url}')
            else:
                image_bucket_url = None
        except Exception as e:
            logger.error("run_python_and_get_png failed: %s", e)
            try:
                question['failed_python_code'] = python_code
                question['failed_python_error'] = str(e)
                python_code = python_graphic_error_fixer(question, visual_instructions, grade, decision, python_code, e)
                png_b64 = run_python_and_get_png(python_code)
                filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
                image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, png_b64)
                logger.info(f'image_bucket_url: {image_bucket_url}')
            except Exception as e:
                logger.error("python_graphic_error_fixer failed: %s", e)
            logger.error("run_python_and_get_png failed: %s", e)
            image_bucket_url = None

        # try:
        #     javascript_code = javascript_graphic_code_generator(question, visual_instructions, grade, decision)
        # except Exception as e: 
        #     logger.error("javascript_graphic_code_generator failed: %s", e)
        #     javascript_code = None
    else:
        logger.info('No graphic needed')
        python_code = None
        #javascript_code = None

    question['visual_instructions'] = visual_instructions
    question['decision'] = decision
    question['python_code'] = python_code
    question['image_url'] = image_bucket_url
    #question['javascript_code'] = javascript_code
    return question

