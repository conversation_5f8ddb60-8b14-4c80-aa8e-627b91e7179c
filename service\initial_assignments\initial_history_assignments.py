from service.context_pipeline import langfuse
from service.openai_utils.gpt_call import make_openai_langfuse
from service.common_functions import contingent_working_style, reading_level_example, character_strength_explanations,life_skills_explanations

from service.context_pipeline import logger, aisdk_object

def generate_initial_history_passage( grade, first_name, working_style, reading_level, life_skills, passage_style, interest, state_standard, standard, assignment_type, add_info, difficulty, strengths, main_topic):
    """Generates the initial passage using extracted inputs (no dependency on state)."""
    ls_explanations = life_skills_explanations(life_skills, standard)
    cs_explanations = character_strength_explanations(strengths)

    passage_explanations = (
        f"Please write a {passage_style} passage aimed at a {difficulty} difficulty level. "
        f"The language, sentence structure, and vocabulary should be appropriate for a(n) "
        f"{reading_level} reading level. This passage must align with the educational standard: "
        f"'{standard}'. Ensure that the style, complexity, and content are all suitable for "
        f"the specified reading level and help students practice skills outlined in {standard}."
    )

    standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')
    working_style_data = contingent_working_style(working_style)
    example_reading_level = reading_level_example(reading_level)

    if assignment_type == 'history_fact':
        logger.info("Generating history fact passage")
        augmentation_prompt = langfuse.get_prompt('history_fact_passage_generation', type='chat', label="latest")

        add_info_instructions = ""
        additional_instructions = ""

        if add_info:
            additional_instructions += f"""
                \nAdditionally, incorporate the following information into the reading passage to further enhance the relevance and engagement for {first_name}: {add_info}.
                Ensure that this information is seamlessly integrated into the storyline and educational objectives."""

        additional_instructions += f"\nPlease ensure the reading passage is suitable for {grade} grade. Ensure that the generated passage is at least 100 words."

        complete_chat_prompt = augmentation_prompt.compile(
            first_name=first_name,
            grade=grade,
            state_standard=state_standard,
            standard=standard_data,
            main_topic=main_topic,
            interests=interest,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            ls_explanations=ls_explanations,
            passage_explanations=passage_explanations,
            additional_instruction=additional_instructions,
            strengths=strengths,
        )

        logger.info(f"complete chat prompt: {complete_chat_prompt}")

        tools = [{
        "type": "function",
        "function": {
            "name": "generate_history_passage",
            "description": (
                "Generates a factual history reading passage suitable for a history assignment. "
                "The passage should be accurate, objective, and reference established historical facts, events, or figures."
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "passage": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the factual, history-based reading passage"
                        }
                    }
                },
                "required": ["passage"]
            }
        }
    }]
    model=augmentation_prompt.config.get("openai", {}).get("model","gpt-4.1")
    temperature=augmentation_prompt.config.get("openai", {}).get("temperature",0.5)
    max_tokens=augmentation_prompt.config.get("openai", {}).get("max_tokens",4096)
    ai_passage = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens)
    logger.info(f"AI passage history fact: {ai_passage}")

    return ai_passage['passage'],tools

def generate_initial_history_assignment(first_name, grade, reading_level, interests, strengths, standard, state_standard, difficulty, add_info, assignment_type, nb_of_questions,main_topic,passage):
    """
    Generates the necessary prompt and tools to create assignments or questions tailored to a student's profile, educational standards,
    and interests. The function dynamically adjusts the generated content based on history assignment type (history_vocab, history_fact)
    and customizes the text to suit the student's working style, reading level, and character strengths.
    """
    logger.info(f"Generating initial history assignment for {first_name}, grade {grade}, assignment type: {assignment_type}")
    logger.debug(f"Parameters - Reading level: {reading_level}, Difficulty: {difficulty}, Questions: {nb_of_questions}")

    tools = []

    try:
        add_info_instructions = ""
        if add_info:
            logger.debug(f"Additional information provided: {add_info}")
            add_info_instructions = f"Make sure to incorporate any additional information provided: {add_info}."

        logger.debug(f"Processing standard data from: {standard}")
        standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')
    except Exception as e:
        logger.error(f"Error processing standard data: {str(e)}")
        standard_data = ""
        add_info_instructions = ""

    if assignment_type == "history_essay":
        logger.info(f"Creating history essay assignment")
        try:
            prompt_template = langfuse.get_prompt("history_essay", type='chat', label="latest")
            logger.debug(f"Retrieved history_essay prompt template")

            complete_chat_prompt = prompt_template.compile(
                state_standard=state_standard,
                nb_of_questions=nb_of_questions,
                first_name=first_name,
                standard=standard_data,
                passage=passage,
                interests=interests,
                grade=grade,
                difficulty=difficulty,
                reading_level=reading_level,
                strengths=strengths,
                add_info_instructions=add_info_instructions,
                main_topic=main_topic
            )
            logger.debug(f"Compiled history_essay prompt template")
        except Exception as e:
            logger.error(f"Error creating history essay prompt: {str(e)}")
            raise

        tools = [
            {
            "type": "function",
                "function": {
                    "name": "generate_essay_prompt",
                    "description": "Generates an essay prompt for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "essay": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the essay prompt"
                                    },
                                    "introduction": {
                                        "type": "string",
                                        "description": "Introduction to the assignment"
                                    },
                                    "background": {
                                        "type": "string",
                                        "description": "background data for the essay prompt"
                                    },
                                    "prompt": {
                                        "type": "string",
                                        "description": "The informational essay prompt"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the essay",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the essay"
                                    }
                                },
                                "required": ["title","introduction", "background", "prompt", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    elif assignment_type == "history_critical":
        logger.info(f"Creating history critical thinking assignment")
        try:
            prompt_template = langfuse.get_prompt("history_critical", type='chat', label="latest")
            logger.debug(f"Retrieved history_critical prompt template")

            complete_chat_prompt = prompt_template.compile(
                state_standard=state_standard,
                nb_of_questions=nb_of_questions,
                first_name=first_name,
                standard=standard_data,
                main_topic=main_topic,
                interests=interests,
                grade=grade,
                difficulty=difficulty,
                reading_level=reading_level,
                strengths=strengths,
                add_info_instructions=add_info_instructions
            )
            logger.debug(f"Compiled history_critical prompt template")
        except Exception as e:
            logger.error(f"Error creating history critical thinking prompt: {str(e)}")
            raise

        tools=[
            {
                "type": "function",
                "function": {
                    "name": "generate_critical_thinking_questions",
                    "description": "Generates questions for a topic that require critical thinking and analysis, based on a provided educational standards.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "homework": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the critical thinking activity"
                                    },
                                    "introduction": {
                                        "type": "string",
                                        "description": "A small text that provides a narrative or factual content from which to derive critical thinking questions, and an introduction to the assignment"
                                    },
                                    "challenges": {
                                        "type": "array",
                                        "description": "A set of questions designed to promote critical analysis of the text, including questions about causality, decision-making, and evaluative opinions",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "index": {
                                                    "type": "string",
                                                    "description": "The question number"
                                                },
                                                "passage": {
                                                    "type": "string",
                                                    "description": "Passage which includes context for the question"
                                                },
                                                "task": {
                                                    "type": "string",
                                                    "description": "The critical thinking question prompt"
                                                },
                                            },
                                            "required": ["task", "passage", "index"]
                                        },
                                    },
                                    "reflection": {
                                        "type": "string",
                                        "description": "Reflection question for the student"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for how to approach and answer the questions, emphasizing critical analysis and reasoning",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the critical thinking activity"
                                    }
                                },
                                "required": ["title", "introduction", "reflection", "challenges", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    elif assignment_type == "history_fact":
        logger.info(f"Creating history fact-based assignment")
        try:
            prompt_template = langfuse.get_prompt("history_fact", type='chat', label="latest")
            logger.debug(f"Retrieved history_fact prompt template")

            complete_chat_prompt = prompt_template.compile(
                first_name=first_name,
                state_standard=state_standard,
                standard=standard_data,
                main_topic=main_topic,
                passage=passage,
                interests=interests,
                grade=grade,
                difficulty=difficulty,
                reading_level=reading_level,
                strengths=strengths,
                nb_of_questions=nb_of_questions,
                add_info_instructions=add_info_instructions,
                
            )
            logger.debug(f"Compiled history_fact prompt template")
        except Exception as e:
            logger.error(f"Error creating history fact-based prompt: {str(e)}")
            raise

        tools = [
            {
                "type": "function",
                "function": {
                    "name": "generate_fact_based_questions",
                    "description": "Generates fact-checking questions for a topic, designed to test direct knowledge without the need for explanatory texts.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "homework": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the fact-checking activity"
                                    },
                                    "introduction": {
                                    "type": "string",
                                    "description": "The complete text providing a concise overview on the main topic"
                                    },
                                    "challenges": {
                                        "type": "array",
                                        "description": "A set of fact-based questions where answers are factual and direct",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "index": {
                                                    "type": "string",
                                                    "description": "The question number"
                                                },
                                                # "passage": {
                                                #     "type": "string",
                                                #     "description": "Passage which includes context for the question"
                                                # },
                                                "task": {
                                                    "type": "string",
                                                    "description": "The fact-checking question prompt"
                                                },
                                                
                                            },
                                            "required": ["task", "index"]
                                        }
                                    },
                                    "reflection": {
                                        "type": "string",
                                        "description": "Reflection question for the student"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the fact-based questions",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the fact-based questions"
                                    }
                                },
                                "required": ["title", "challenges", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    else:
        logger.error(f"Unknown assignment type: {assignment_type}")
        raise ValueError(f"Unknown assignment type: {assignment_type}")

    try:
        logger.info(f"Making OpenAI API call with langfuse tracking")
        temperature = prompt_template.config.get("openai", {}).get("temperature",0.5)
        model = prompt_template.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = prompt_template.config.get("openai", {}).get("max_tokens",4096)
        assignment = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb_of_questions)
        logger.info(f"Successfully generated {assignment_type} assignment")
        logger.debug(f"Assignment response received")

        return assignment,tools
    except Exception as e:
        logger.error(f"Error in OpenAI API call: {str(e)}")
        raise