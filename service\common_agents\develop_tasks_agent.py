from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
def develop_tasks_agent(extract_skills_output:dict,grade:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
    grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
    if type(extract_skills_output) == str:
        try:
            extract_skills_output = json.loads(extract_skills_output)
        except Exception as e:
            logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
            
    messages = langfuse.get_prompt('develop tasks', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        extract_skills_output=extract_skills_output,
        grade_info=grade_info
    )
    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_skill_demonstrations",
                "description": (
                    "Produces an ordered list of skill objects, each containing a skill "
                    "description and a corresponding demonstration example."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skills": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill' field and a 'demonstration_example' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {
                                        "type": "string",
                                        "description": "The number of the skill"
                                    },
                                    "task": {
                                        "type": "string",
                                        "description": "A task that measures the skill."
                                    }
                                },
                                "required": ["skill_number", "task"]
                            }
                        }
                    },
                    "required": ["skills"]
                }
            }
        }
    ]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
    # skill_map = {item['skill_number']: {'skill': item['skill'], 'demonstration_example': item['demonstration_example']} for item in extract_skills_output}
    
    # for task in result['skills']:
    #     sn = task.get('skill_number')
    #     if 'skill' not in task or 'demonstration_example' not in task:
    #         skill_info = skill_map.get(sn)
    #         if skill_info:
    #             task.setdefault('skill', skill_info['skill'])
    #             task.setdefault('demonstration_example', skill_info['demonstration_example'])
    return result
