# from service.openai_utils.gpt_call import make_openai_langfuse
# from service.context_pipeline import langfuse
# from service.context_pipeline import logger
# import json
# def develop_tasks_agent(extract_skills_output:dict,grade:str,number_of_questions:int) -> dict:
#     """
#     Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

#     Args:
#         original_assignment (str): Original student assignment content.
#         adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

#     Returns:
#         dict: Revised assignment and implemented adjustments.
#     """
#     grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
#     if type(number_of_questions) == str:
#         number_of_questions = int(number_of_questions)
#     if type(extract_skills_output) == str:
#         try:
#             extract_skills_output = json.loads(extract_skills_output)
#         except Exception as e:
#             logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
            
#     messages = langfuse.get_prompt('develop tasks', type='chat', label="latest")

#     complete_chat_prompt = messages.compile(
#         extract_skills_output=extract_skills_output,
#         grade_info=grade_info
#     )
#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "generate_tasks",
#                 "description": (
#                     "Produces an ordered list of tasks, each containing a task "
#                     "description and a corresponding demonstration example."
#                     ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "skills": {
#                             "type": "array",
#                             "description": (
#                                 "A list of skill objects. Each object must include a "
#                                 "'skill_number' field and a 'task' field."
#                             ),
#                             "items": {
#                                 "type": "object",
#                                 "properties": {
#                                     "skill_number": {
#                                         "type": "string",
#                                         "description": "The number of the skill"
#                                     },
#                                     "task": {
#                                         "type": "string",
#                                         "description": "A task that measures the skill."
#                                     }
#                                 },
#                                 "required": ["skill_number", "task"]
#                             }
#                         }
#                     },
#                     "required": ["skills"]
#                 }
#             }
#         }
#     ]
#     temperature = messages.config.get("openai", {}).get("temperature", 0.5)
#     model = messages.config.get("openai", {}).get("model", "gpt-4.1")
#     max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
#     result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
#     skill_map = {item['skill_number']: {'skill': item['skill'], 'demonstration_example': item['demonstration_example']} for item in extract_skills_output}
    
#     for task in result['skills']:
#         sn = task.get('skill_number')
#         if 'skill' not in task or 'demonstration_example' not in task:
#             skill_info = skill_map.get(sn)
#             if skill_info:
#                 task.setdefault('skill', skill_info['skill'])
#                 task.setdefault('demonstration_example', skill_info['demonstration_example'])
#     return result['skills']

from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json

def develop_tasks_agent( extract_skills_output: dict, grade: str, nb_of_questions: int,assignment_type: str, main_topic:str, ) -> dict:
    """
    Generates `nb_of_questions` tasks based on the provided sub-standards.

    Behaviour:
    1. If `nb_of_questions` <= len(substandards)  ➔ trim substandards list to the
       first `nb_of_questions` items and produce that many tasks (single API call).
    2. If `nb_of_questions`  > len(substandards) ➔ repeatedly loop through the
       substandards list, making additional API calls until the required number
       of tasks is reached.

    Args:
        extract_skills_output (dict | str): List of sub-standard objects.
        grade (str): Grade level.
        nb_of_questions (int): Total number of tasks required.

    Returns:
        list[dict]: List of generated task objects.
    """
    grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
    additional_instruction = ""

    if 'history' in assignment_type:
        if main_topic:
            additional_instruction += f"The main topic of the assignment is {main_topic}."
    elif 'math' in assignment_type:
        additional_instruction = """
        GRAPHICS LIMITATIONS FOR ALL TASKS

        - No pre-made or uploaded pictures can be handed to students.
        - You may refer to any shape, but only those that can be created using simple outline-only code (not to scale, not editable) can be generated.
        - Students have only a small free-hand draw box with one pen tool; they cannot move, resize, or rotate shapes that appear.
        - Do NOT require rulers, protractors, folding, cutting, or precise measurement.
        - Tasks may ask students to: • draw their own shapes or symmetry lines in the draw box.
        - Avoid wording like “look at the picture above” or “trace over the shape provided.” Replace with “In your draw box, sketch…” 
        - All drawing actions must be limited to the website’s draw box.
        - Do not instruct students to draw anywhere else (e.g., notebooks, paper, etc.).
        """
    # Convert to list if passed as JSON string
    if isinstance(extract_skills_output, str):
        try:
            extract_skills_output = json.loads(extract_skills_output)
        except Exception as e:
            logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
            extract_skills_output = []

    # # Mapping for filling missing fields later
    skill_map = {
        item["skill_number"]: {
            "skill": item.get("skill"),
            "demonstration_example": item.get("demonstration_example")
        }
        for item in extract_skills_output
    }

    # Prepare prompt template & OpenAI config once
    messages = langfuse.get_prompt("develop tasks", type="chat", label="latest")
    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_tasks",
                "description": (
                    "Produces an ordered list of skill objects, each containing "
                    "a skill_number and a list of tasks containing 2 task"
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skills": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill_number' field and a 'tasks' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {"type": "string",
                                                     "description": "The number of the skill"},
                                    "tasks": {"type": "array",
                                             "description": "A list of tasks that measures the skill.",
                                             "items": {"type": "string",
                                                        "description": "A task that measures the skill."}}
                                },
                                "required": ["skill_number", "tasks"]
                            }
                        }
                    },
                    "required": ["skills"]
                }
            }
        }
    ]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = 8000

    total_substandards = len(extract_skills_output)
    tasks_generated = []

    while len(tasks_generated) < nb_of_questions and total_substandards:
        remaining_needed = nb_of_questions - len(tasks_generated)
        if remaining_needed % 2 == 0:
            remaining_needed=remaining_needed//2
        else:
            remaining_needed=(remaining_needed//2)+1
        # Slice the list based on how many tasks are still required
        current_slice = (
            extract_skills_output[:remaining_needed]
            if remaining_needed < total_substandards
            else extract_skills_output
        )
        logger.info(f"Current slice: {current_slice}")
        # Compile prompt with the current slice
        complete_chat_prompt = messages.compile(
            extract_skills_output=current_slice,
            grade_info=grade_info,
            additional_instrurctions=additional_instruction
        )

        # Call the model
        result = make_openai_langfuse(
            complete_chat_prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            tools=tools
        )
        output = []
        for item in result['skills']:
            skill_number = item['skill_number']
            for task in item['tasks']:
                output.append({'skill_number': skill_number, 'task': task})

        # Post-process returned tasks
        for task in output:
            sn = task.get("skill_number")
            # info = skill_map.get(sn, {})
            # task.setdefault("skill", info.get("skill"))
            # task.setdefault("demonstration_example", info.get("demonstration_example"))
            tasks_generated.append(task)

    # Trim to exactly nb_of_questions and return
    return tasks_generated[:nb_of_questions]