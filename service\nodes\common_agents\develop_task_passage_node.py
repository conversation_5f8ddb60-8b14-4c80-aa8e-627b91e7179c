from service.gen_data_models import GeneratePipelineContext
from service.common_agents.develop_task_passage import develop_tasks_passage_agent
from service.context_pipeline import logger

def develop_tasks_passage_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting develop_tasks_passage_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    grade=state.student.grade
    passage=state.ai_passage
    nb_of_questions = state.request.nb_of_questions
    assignment_type=state.request.assignment_type
    #sub_standard_data={}

    if 'skills' in sub_standard:
        sub_standard=sub_standard['skills']
    try:
        response = develop_tasks_passage_agent(
            refined_passage=passage,
            sub_standard=sub_standard,
            grade=grade,
            nb_of_questions=nb_of_questions,
            assignment_type=assignment_type
        )

        logger.info(f"develop_tasks_passage response: {response}")
    except Exception as e:
        logger.error(f"Error in develop_tasks_passage_node: {e}")

    return {"enriched_tasks": response, "substandards_data": sub_standard}