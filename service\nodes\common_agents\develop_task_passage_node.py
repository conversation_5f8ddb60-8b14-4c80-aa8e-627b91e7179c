from service.gen_data_models import GeneratePipelineContext
from service.common_agents.develop_task_passage import develop_tasks__passage_agent
from service.context_pipeline import logger

def develop_tasks_passage_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting develop_tasks_passage_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    grade=state.student.grade
    passage=state.ai_passage
    try:
        response = develop_tasks__passage_agent(
            refined_passage=passage,
            sub_standard=sub_standard,
            grade=grade
        )

        logger.info(f"develop_tasks_passage response: {response}")
    except Exception as e:
        logger.error(f"Error in develop_tasks_passage_node: {e}")

    return {"enriched_tasks": response}