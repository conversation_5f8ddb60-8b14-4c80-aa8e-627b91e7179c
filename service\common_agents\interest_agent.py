from service.openai_utils.gpt_call import make_openai_langfuse,make_openai_langfuse_websearch
from service.context_pipeline import logger, aisdk_object
from service.context_pipeline import langfuse
import random



def interest_search_agent(interest,grade,search_queries):    
    messages = langfuse.get_prompt('interest_search_agent', type='chat', label="latest")
    prompt = messages.compile(interest=interest, grade=grade,search_queries=search_queries)
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    response=make_openai_langfuse_websearch(prompt, model, temperature,max_tokens)
    return response

def supporting_interest_agent(initial_assignment: dict, interest_information: str, nb: int, assignment_type: str, assignment_state: str) -> dict:
    """
    Aligns assignments to student interests by generating fun facts and current information
    using a single OpenAI API call with web search capability.

    Returns:
        dict: {
            "adjustment_instructions": str (empty if already included)
        }
    """
    tools = [
        {
            "type": "function",
            "function": {
                "name": "check_feedback_for_interest_reference",
                "description": "Evaluate whether the feedback incorporates the student's personal interests to make it more engaging and relevant.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "interest_referenced": {
                            "type": "boolean",
                            "description": "Indicates whether the feedback includes reference to the student’s personal interests or real-world connections that matter to them."
                        },
                        "reasoning": {
                            "type": "string",
                            "description": "Explanation of why the feedback does or does not reference the student’s interests."
                        },
                        "interest_guidance": {
                            "type": "string",
                            "description": "If interest is referenced, explain how it supports engagement and how to maintain or expand it. If not, suggest specific ways to tailor the feedback using student interests."
                        }
                    },
                    "required": ["interest_referenced", "reasoning", "interest_guidance"]
                }
            }
        }
    ]

    logger.info("Checking if the assignment already includes the interest.")
    messages = langfuse.get_prompt('Interest_agent_for_updaton', type='chat', label="latest")
    complete_chat_prompt = messages.compile(
        initial_assignment=initial_assignment,
        interesting_information=interest_information,
    )
    
    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
    result = make_openai_langfuse(complete_chat_prompt, temperature=temperature, model=model, max_tokens=max_tokens,tools=tools)
    return result

def _build_general_information(interest, grade):
    sub_category = random.choice([
        'interest_fun_facts_agent',
        'interest_historical_facts',
        'interest_famous_personalities',
        'interest_related_topics'
    ])
    logger.info(f"Generating information for {sub_category}")

    messages      = langfuse.get_prompt(sub_category, type='chat', label="latest")
    prompt        = messages.compile(interest=interest, grade=grade)
    temperature   = messages.config.get("openai", {}).get("temperature", 0.5)
    model         = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens    = messages.config.get("openai", {}).get("max_tokens", 4096)
    tools = [{
        "type": "function",
        "function": {
            "name": "generate_agent_information",
            "description": "Generates a structured list of information based on the agent's predefined category of information focus.",
            "parameters": {
                "type": "object",
                "properties": {
                    "information": {
                        "type": "array",
                        "description": "Generated list of information entries tailored specifically to agent's category",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name":        {"type": "string", "description": "Name/title of the generated information"},
                                "description": {"type": "string", "description": "Detailed description of the generated information"}
                            },
                            "required": ["name", "description"]
                        }
                    }
                },
                "required": ["information"]
            }
        }
    }]

    result = make_openai_langfuse(prompt,
                                  temperature=temperature,
                                  model=model,
                                  max_tokens=max_tokens,
                                  tools=tools)
    logger.info(f"General Interest Information: {result}")
    return str(result['information'])


def choose_random_interest_information(interest, grade):
    """
    Tries to return 'real-time' information first (50 % chance).
    If the real-time path fails, automatically falls back to 'general'.
    """
    main_category = random.choice(['real_time', 'general'])
    logger.info(f"Main category chosen: {main_category}")

    if main_category == 'real_time':
        try:
            sub_category = random.choice([
                'interest_search_news',
                'interest_search_trends_and_events',
                'interest_search_popular_discussions'
            ])
            logger.info(f"Generating information for {sub_category}")

            messages      = langfuse.get_prompt(sub_category, type='chat', label="latest")
            prompt        = messages.compile(interest=interest, grade=grade)
            temperature   = messages.config.get("openai", {}).get("temperature", 0.5)
            model         = messages.config.get("openai", {}).get("model", "gpt-4.1")
            max_tokens    = messages.config.get("openai", {}).get("max_tokens", 4096)
            tools = [{
                "type": "function",
                "function": {
                    "name": "generate_search_queries",
                    "description": "Generate a list of search queries related to a specific topic or interest.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "search_queries": {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "description": "A relevant search query related to the topic or interest provided."
                                },
                                "description": "List of search query strings."
                            }
                        },
                        "required": ["search_queries"]
                    }
                }
            }]

            result = make_openai_langfuse(prompt,
                                          temperature=temperature,
                                          model=model,
                                          max_tokens=max_tokens,
                                          tools=tools)
            logger.info(f"Search Queries: {result}")

            interest_information = interest_search_agent(
                interest, grade, result['search_queries'])
            logger.info(f"Interest Information: {interest_information}")

            return str(interest_information)

        except Exception as e:
            logger.error(f"Real-time search failed ({e}). Falling back to general.")

    # Either the random choice was 'general' or the real-time path failed:
    return _build_general_information(interest, grade)
