from service.gen_data_models import GeneratePipelineContext
from service.common_agents.rewrite_passage_to_align_substandard import rewrite_passage_to_align_substandard_agent
from service.context_pipeline import logger

def rewrite_passage_to_align_substandard_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting rewrite_passage_to_align_substandard_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    passage=state.ai_passage
    if "inspect_passage_justification" in adjustment_instructions:
        justification=adjustment_instructions['inspect_passage_justification']
    else:
        logger.error("rewrite_passage_substandard_called without inspect_passage_justification")
        return state
    try:
        response = rewrite_passage_to_align_substandard_agent(
            passage=passage,
            sub_standard=sub_standard,
            justification=justification
        )
        logger.info(f"rewrite_passage_to_align_substandard response: {response}")
        if 'passage' in response:
            response = response['passage']
        logger.info("Passage successfully revised to align with substandard.")
        return {'ai_passage': response}
    except Exception as e:
        logger.error(f"Error in rewrite_passage_to_align_substandard_node: {e}")

