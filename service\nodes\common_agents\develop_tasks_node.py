from service.gen_data_models import GeneratePipelineContext
from service.common_agents.develop_tasks_agent import develop_tasks_agent
from service.context_pipeline import logger

def develop_tasks_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting develop_tasks_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    extract_skills_output=state.processed_standard
    grade=state.student.grade
    # if not adjustment_instructions:
    #     logger.info("No adjustments provided. develop_tasks node skipped.")
    #     return {}
    if 'skills' in extract_skills_output:
        extract_skills_output=extract_skills_output['skills']
    try:
        response = develop_tasks_agent(
            extract_skills_output=extract_skills_output,
            grade=grade
        )

        logger.info(f"develop_tasks response: {response}")
        adjustment_instructions['develop_tasks'] = response
    except Exception as e:
        logger.error(f"Error in develop_tasks_node: {e}")

    return {"adjustment_instructions": adjustment_instructions}