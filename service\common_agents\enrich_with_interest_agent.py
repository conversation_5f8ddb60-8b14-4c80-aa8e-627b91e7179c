from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
def enrich_with_interest_agent(grade:str,subject:str,skill:str,task:str,interesting_information: dict) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
    logger.info(f"Starting enrich_with_interest_agent with interest: {interesting_information}")
    if type(interesting_information) == list:
        interesting_information=interesting_information[0]
    messages = langfuse.get_prompt('enrich_with_interest', type='chat', label="latest")
    if 'name' in interesting_information and 'description' in interesting_information:
        try:
            interest_name=interesting_information['name']
            interest_description=interesting_information['description']    
        except Exception as e:
            logger.error(f"Failed to extract interest_name and interest_description: {e}")
            if type(interesting_information) == str:
                try:
                    interest_name=""
                    interest_description=interesting_information
                except Exception as e:
                    logger.error(f"Failed to extract interest_name and interest_description: {e}")
                    interest_name=''
                    interest_description=''

    complete_chat_prompt = messages.compile(
        grade=grade,
        subject=subject,
        skill=skill,
        task=task,
        interest_name=interest_name,
        interest_description=interest_description
        )
    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_enriched_task",
                "description": (
                    "Given a learning activity context (including details like skill, task, demonstration_example, and student interest), "
                    "generates a creative, engaging, and contextually relevant 'enriched_task'—that is, a more vivid or thematic version of the original task using the provided 'interest' context. "
                    "The enriched task should connect the mathematics to the student's interest and encourage imaginative thinking or role-play."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "enriched_task": {
                            "type": "string",
                            "description": (
                                "A creative, interest-based rewrite of the original mathematical task which should be engaging, relatable, "
                                "and clearly grounded in both the mathematical skill and the provided interest context."
                            )
                        }
                    },
                    "required": ["enriched_task"]
                }
            }
        }
    ]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    return result['enriched_task']
